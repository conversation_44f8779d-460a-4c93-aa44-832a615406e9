# 紧急低功耗配置 - 用于解决Brownout问题
# 临时禁用一些功能来减少启动时的功耗

esphome:
  name: 16-ch-monitor-test
  platform: ESP32
  board: esp32dev
  # 降低CPU频率以减少功耗
  platformio_options:
    board_build.f_cpu: 160000000L  # 降低到160MHz

# WiFi配置 - 降低功率
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password
  power_save_mode: HIGH  # 启用高功耗节省模式
  output_power: 10dB     # 降低WiFi发射功率

# 日志级别降低以减少串口输出功耗
logger:
  level: WARN  # 只显示警告和错误
  baud_rate: 115200

# API配置
api:
  encryption:
    key: !secret api_encryption_key

# OTA配置
ota:
  password: !secret ota_password

# Web服务器 - 临时禁用
# web_server:
#   port: 80

# 基本UART配置
uart:
  - id: uart_bus1
    tx_pin: GPIO1
    rx_pin: GPIO3
    baud_rate: 9600
  - id: uart_bus2
    tx_pin: GPIO17
    rx_pin: GPIO16
    baud_rate: 9600

# 简化的BL0906配置 - 只保留基本功能
bl0906_factory:
  - id: sensor_bl0906
    communication: uart
    uart_id: uart_bus1
    update_interval: 10s  # 增加更新间隔以减少功耗
    instance_id: 0x906B0001
    calibration_mode: true
    calibration:
      enabled: true
      storage_type: preference

# 只保留最基本的传感器
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    frequency:
      name: 'BL0906 Frequency'
    voltage:
      name: 'BL0906 Voltage'
    temperature:
      name: 'BL0906 Temperature'

# 紧急恢复按钮
button:
  - platform: template
    name: "Emergency Diagnose NVS"
    on_press:
      lambda: |-
        ESP_LOGI("emergency", "开始紧急NVS诊断...");
        id(sensor_bl0906)->diagnose_nvs_storage();

  - platform: template
    name: "Emergency Force Recovery"
    on_press:
      lambda: |-
        ESP_LOGI("emergency", "开始紧急强制恢复...");
        id(sensor_bl0906)->force_recover_calibration_data();

  - platform: template
    name: "Emergency Reload Calibration"
    on_press:
      lambda: |-
        ESP_LOGI("emergency", "紧急重新加载校准数据...");
        if (id(sensor_bl0906)->reload_calibration_data_to_chip()) {
          ESP_LOGI("emergency", "✅ 校准数据重新加载成功");
        } else {
          ESP_LOGE("emergency", "❌ 校准数据重新加载失败");
        }

# 状态LED - 用于指示设备状态
status_led:
  pin: GPIO2

# 重启按钮 - 用于手动重启
switch:
  - platform: restart
    name: "Emergency Restart"

#include "preference_calibration_storage.h"
#include "esphome/core/application.h"

namespace esphome {
namespace bl0906_factory {

const char *const PreferenceCalibrationStorage::TAG = "bl0906.preference_storage";

PreferenceCalibrationStorage::PreferenceCalibrationStorage() {
}

bool PreferenceCalibrationStorage::init() {
    ESP_LOGD(TAG, "初始化preference校准存储");
    return load_instance_list();
}

// 实现基类的原始数据操作接口
StorageResult PreferenceCalibrationStorage::read_raw_data(uint32_t instance_id, uint8_t* buffer, size_t& buffer_size) {
    std::string key = get_preference_key(instance_id);
    
    // 先尝试读取实际数据长度
    auto size_pref = global_preferences->make_preference<uint16_t>(fnv1_hash(key + "_size"));
    uint16_t actual_size = 0;
    
    if (!size_pref.load(&actual_size) || actual_size == 0) {
        return StorageResult::INSTANCE_NOT_FOUND;
    }
    
    // 检查缓冲区大小
    if (buffer_size < actual_size) {
        buffer_size = actual_size;  // 告诉调用者需要的大小
        return StorageResult::INVALID_DATA;
    }
    
    // 读取实际数据
    auto pref = global_preferences->make_preference<uint8_t[512]>(fnv1_hash(key));
    uint8_t temp_data[512];
    
    if (!pref.load(&temp_data)) {
        return StorageResult::INSTANCE_NOT_FOUND;
    }
    
    // 只复制实际数据长度，避免被误识别为EEPROM格式
    memcpy(buffer, temp_data, actual_size);
    buffer_size = actual_size;
    
    return StorageResult::SUCCESS;
}

StorageResult PreferenceCalibrationStorage::write_raw_data(uint32_t instance_id, const uint8_t* buffer, size_t buffer_size) {
    if (buffer_size > 512) {
        return StorageResult::INVALID_DATA;
    }

    std::string key = get_preference_key(instance_id);

    // 保存实际数据长度
    auto size_pref = global_preferences->make_preference<uint16_t>(fnv1_hash(key + "_size"));
    uint16_t size_to_save = static_cast<uint16_t>(buffer_size);
    if (!size_pref.save(&size_to_save)) {
        ESP_LOGE(TAG, "保存数据长度失败，实例: 0x%08X", instance_id);
        return StorageResult::IO_ERROR;
    }

    // 准备固定大小的数据缓冲区
    uint8_t data[512] = {0};
    memcpy(data, buffer, buffer_size);

    auto pref = global_preferences->make_preference<uint8_t[512]>(fnv1_hash(key));
    if (!pref.save(&data)) {
        ESP_LOGE(TAG, "保存数据失败，实例: 0x%08X", instance_id);
        return StorageResult::IO_ERROR;
    }

    // 立即验证写入的数据
    uint8_t verify_data[512];
    if (!pref.load(&verify_data) || memcmp(data, verify_data, buffer_size) != 0) {
        ESP_LOGE(TAG, "数据写入验证失败，实例: 0x%08X", instance_id);
        return StorageResult::IO_ERROR;
    }

    ESP_LOGD(TAG, "实例 0x%08X 数据写入并验证成功 (%d 字节)", instance_id, buffer_size);

    // 更新实例列表
    add_to_instance_list(instance_list_, instance_id);
    bool save_success = save_instance_list();
    if (!save_success) {
        ESP_LOGE(TAG, "保存实例列表失败！");
        return StorageResult::IO_ERROR;
    }
    ESP_LOGD(TAG, "实例列表已更新，当前包含 %d 个实例", instance_list_.size());

    return StorageResult::SUCCESS;
}

StorageResult PreferenceCalibrationStorage::delete_raw_data(uint32_t instance_id) {
    std::string key = get_preference_key(instance_id);
    
    // 删除数据长度记录
    auto size_pref = global_preferences->make_preference<uint16_t>(fnv1_hash(key + "_size"));
    uint16_t zero_size = 0;
    size_pref.save(&zero_size);
    
    // ESPPreferenceObject 没有 reset() 方法，我们通过保存空数据来"删除"
    auto pref = global_preferences->make_preference<uint8_t[512]>(fnv1_hash(key));
    uint8_t empty_data[512] = {0};
    if (!pref.save(&empty_data)) {
        return StorageResult::IO_ERROR;
    }
    
    // 从实例列表中移除
    remove_from_instance_list(instance_list_, instance_id);
    bool save_success = save_instance_list();
    if (!save_success) {
        ESP_LOGE(TAG, "保存实例列表失败！");
        return StorageResult::IO_ERROR;
    }
    ESP_LOGD(TAG, "从实例列表中移除实例 0x%08X，当前包含 %d 个实例", instance_id, instance_list_.size());
    
    return StorageResult::SUCCESS;
}

bool PreferenceCalibrationStorage::verify() {
    // preference存储由ESP32 NVS保证数据完整性
    return true;
}

bool PreferenceCalibrationStorage::erase() {
    // 删除所有实例
    for (uint32_t instance_id : instance_list_) {
        delete_raw_data(instance_id);
    }
    instance_list_.clear();
    save_instance_list();
    
    ESP_LOGD(TAG, "清除所有校准数据");
    return true;
}

std::vector<uint32_t> PreferenceCalibrationStorage::get_instance_list() {
    return instance_list_;
}

// Private helper methods
std::string PreferenceCalibrationStorage::get_preference_key(uint32_t instance_id) {
    char key[32];
    snprintf(key, sizeof(key), "bl0906_cal_%08X", instance_id);
    return std::string(key);
}

bool PreferenceCalibrationStorage::save_instance_list() {
    // 使用简化的数组大小，最多4个实例
    auto pref = global_preferences->make_preference<uint32_t[5]>(fnv1_hash("bl0906_factory_instances"));

    uint32_t data[5] = {0};
    size_t count = std::min(instance_list_.size(), size_t(4));
    data[0] = count;

    for (size_t i = 0; i < count; i++) {
        data[i + 1] = instance_list_[i];
        ESP_LOGD(TAG, "  保存实例[%d]: 0x%08X", i, instance_list_[i]);
    }

    ESP_LOGD(TAG, "保存 %d 个实例到列表", count);
    bool result = pref.save(&data);
    if (result) {
        ESP_LOGD(TAG, "实例列表保存成功");
        // 同时创建备份
        create_backup_instance_list();
    } else {
        ESP_LOGE(TAG, "实例列表保存失败");
    }
    return result;
}

bool PreferenceCalibrationStorage::load_instance_list() {
    // 使用简化的数组大小，最多4个实例
    auto pref = global_preferences->make_preference<uint32_t[5]>(fnv1_hash("bl0906_factory_instances"));

    uint32_t data[5];
    if (!pref.load(&data)) {
        ESP_LOGD(TAG, "实例列表不存在，扫描现有实例");
        instance_list_.clear();

        // 扫描已存在的实例数据
        scan_existing_instances();

        // 如果常规扫描没有找到实例，尝试强制恢复
        if (instance_list_.empty()) {
            ESP_LOGW(TAG, "常规扫描未找到实例，尝试强制数据恢复...");
            force_data_recovery();
        }

        // 如果找到实例，保存到列表中
        if (!instance_list_.empty()) {
            save_instance_list();
            ESP_LOGD(TAG, "扫描并保存了 %d 个现有实例", instance_list_.size());
        }
        return true;
    }

    uint32_t count = data[0];
    if (count > 4) {
        ESP_LOGE(TAG, "实例列表数量异常: %d", count);
        return false;
    }

    instance_list_.clear();
    instance_list_.reserve(count);

    for (uint32_t i = 0; i < count; i++) {
        uint32_t instance_id = data[i + 1];

        // 验证实例是否真的存在
        if (verify_instance_exists(instance_id)) {
            instance_list_.push_back(instance_id);
            ESP_LOGD(TAG, "  加载实例: 0x%08X", instance_id);
        } else {
            ESP_LOGW(TAG, "  实例 0x%08X 在列表中但数据不存在，跳过", instance_id);
        }
    }

    ESP_LOGD(TAG, "加载了 %d 个有效实例", instance_list_.size());

    // 如果发现实例列表有变化，重新保存
    if (instance_list_.size() != count) {
        save_instance_list();
    }

    return true;
}

void PreferenceCalibrationStorage::scan_existing_instances() {
    ESP_LOGD(TAG, "开始简化扫描现有实例...");

    // 限制最大实例数量为4个
    const uint32_t MAX_INSTANCES = 4;

    // 首先尝试从备份加载
    auto backup_pref = global_preferences->make_preference<uint32_t[5]>(fnv1_hash("bl0906_instances_backup"));
    uint32_t backup_data[5] = {0};

    if (backup_pref.load(&backup_data)) {
        uint32_t backup_count = backup_data[0];
        if (backup_count > 0 && backup_count <= MAX_INSTANCES) {
            ESP_LOGD(TAG, "从备份发现 %d 个实例", backup_count);

            for (uint32_t i = 0; i < backup_count; i++) {
                uint32_t instance_id = backup_data[i + 1];
                if (verify_instance_exists(instance_id)) {
                    instance_list_.push_back(instance_id);
                    ESP_LOGD(TAG, "恢复实例: 0x%08X", instance_id);
                }
            }

            if (!instance_list_.empty()) {
                ESP_LOGD(TAG, "从备份恢复 %d 个实例", instance_list_.size());
                return;
            }
        }
    }

    // 如果备份失败，只扫描已知的实例ID
    ESP_LOGD(TAG, "备份不可用，扫描已知实例...");

    // 只检查最常用的4个实例ID
    uint32_t known_instances[] = {
        0x906B0001,  // BL0906 实例1
        0x910B0001,  // BL0910 实例1
        0x906B0002,  // BL0906 实例2
        0x910B0002   // BL0910 实例2
    };

    for (uint32_t instance_id : known_instances) {
        if (verify_instance_exists(instance_id)) {
            instance_list_.push_back(instance_id);
            ESP_LOGD(TAG, "发现实例: 0x%08X", instance_id);
        }

        // 限制最大实例数量
        if (instance_list_.size() >= MAX_INSTANCES) {
            break;
        }
    }

    ESP_LOGD(TAG, "扫描完成，发现 %d 个实例", instance_list_.size());

    // 创建备份
    if (!instance_list_.empty()) {
        create_backup_instance_list();
    }
}

bool PreferenceCalibrationStorage::verify_instance_exists(uint32_t instance_id) {
    std::string key = get_preference_key(instance_id);

    // 检查数据长度记录是否存在且有效
    auto size_pref = global_preferences->make_preference<uint16_t>(fnv1_hash(key + "_size"));
    uint16_t actual_size = 0;

    if (!size_pref.load(&actual_size) || actual_size == 0) {
        ESP_LOGD(TAG, "实例 0x%08X 的大小记录不存在或为0", instance_id);
        return false;  // 数据不存在或长度为0
    }

    // 尝试读取数据验证
    auto pref = global_preferences->make_preference<uint8_t[512]>(fnv1_hash(key));
    uint8_t temp_data[512];

    bool data_exists = pref.load(&temp_data);
    ESP_LOGD(TAG, "实例 0x%08X 验证结果: 大小=%d, 数据存在=%s",
             instance_id, actual_size, data_exists ? "是" : "否");

    return data_exists;  // 能成功加载说明存在
}

void PreferenceCalibrationStorage::create_backup_instance_list() {
    ESP_LOGD(TAG, "创建简化实例列表备份...");

    // 减少备份数组大小，最多4个实例
    auto backup_pref = global_preferences->make_preference<uint32_t[5]>(fnv1_hash("bl0906_instances_backup"));

    uint32_t backup_data[5] = {0};
    size_t count = std::min(instance_list_.size(), size_t(4));
    backup_data[0] = count;

    for (size_t i = 0; i < count; i++) {
        backup_data[i + 1] = instance_list_[i];
    }

    if (backup_pref.save(&backup_data)) {
        ESP_LOGD(TAG, "实例列表备份成功，保存了 %d 个实例", count);
    } else {
        ESP_LOGE(TAG, "实例列表备份失败");
    }
}

// 新增：简化的强制数据恢复方法
bool PreferenceCalibrationStorage::force_data_recovery() {
    ESP_LOGI(TAG, "开始简化强制数据恢复...");

    instance_list_.clear();
    int recovered_count = 0;
    const uint32_t MAX_INSTANCES = 4;

    // 只检查已知的实例ID，避免大范围扫描
    uint32_t known_instances[] = {
        0x906B0001,  // BL0906 实例1
        0x910B0001,  // BL0910 实例1
        0x906B0002,  // BL0906 实例2
        0x910B0002   // BL0910 实例2
    };

    ESP_LOGI(TAG, "检查已知实例...");
    for (uint32_t instance_id : known_instances) {
        std::string key = get_preference_key(instance_id);

        auto size_pref = global_preferences->make_preference<uint16_t>(fnv1_hash(key + "_size"));
        uint16_t actual_size = 0;

        if (size_pref.load(&actual_size) && actual_size > 0) {
            auto pref = global_preferences->make_preference<uint8_t[512]>(fnv1_hash(key));
            uint8_t temp_data[512];

            if (pref.load(&temp_data)) {
                instance_list_.push_back(instance_id);
                recovered_count++;
                ESP_LOGI(TAG, "✅ 恢复实例: 0x%08X (大小: %d 字节)", instance_id, actual_size);

                // 限制最大实例数量
                if (recovered_count >= MAX_INSTANCES) {
                    break;
                }
            }
        }
    }

    ESP_LOGI(TAG, "简化强制数据恢复完成，恢复了 %d 个实例", recovered_count);

    if (recovered_count > 0) {
        // 重建实例列表和备份
        save_instance_list();
        ESP_LOGI(TAG, "实例列表已重建");
        return true;
    }

    return false;
}

// 新增：NVS诊断方法
void PreferenceCalibrationStorage::diagnose_nvs_storage(uint32_t instance_id) {
    ESP_LOGI(TAG, "=== NVS存储诊断 (实例: 0x%08X) ===", instance_id);

    std::string key = get_preference_key(instance_id);
    ESP_LOGI(TAG, "Preference Key: %s", key.c_str());

    // 检查大小记录
    auto size_pref = global_preferences->make_preference<uint16_t>(fnv1_hash(key + "_size"));
    uint16_t actual_size = 0;
    bool size_exists = size_pref.load(&actual_size);

    ESP_LOGI(TAG, "大小记录:");
    ESP_LOGI(TAG, "  Key: %s_size", key.c_str());
    ESP_LOGI(TAG, "  Hash: 0x%08X", fnv1_hash(key + "_size"));
    ESP_LOGI(TAG, "  存在: %s", size_exists ? "是" : "否");
    ESP_LOGI(TAG, "  大小: %d 字节", actual_size);

    // 检查数据记录
    auto pref = global_preferences->make_preference<uint8_t[512]>(fnv1_hash(key));
    uint8_t temp_data[512];
    bool data_exists = pref.load(&temp_data);

    ESP_LOGI(TAG, "数据记录:");
    ESP_LOGI(TAG, "  Key: %s", key.c_str());
    ESP_LOGI(TAG, "  Hash: 0x%08X", fnv1_hash(key));
    ESP_LOGI(TAG, "  存在: %s", data_exists ? "是" : "否");

    if (data_exists && actual_size > 0) {
        ESP_LOGI(TAG, "  前16字节数据: %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X",
                 temp_data[0], temp_data[1], temp_data[2], temp_data[3],
                 temp_data[4], temp_data[5], temp_data[6], temp_data[7],
                 temp_data[8], temp_data[9], temp_data[10], temp_data[11],
                 temp_data[12], temp_data[13], temp_data[14], temp_data[15]);
    }

    // 检查实例列表
    auto list_pref = global_preferences->make_preference<uint32_t[5]>(fnv1_hash("bl0906_factory_instances"));
    uint32_t list_data[5];
    bool list_exists = list_pref.load(&list_data);

    ESP_LOGI(TAG, "实例列表:");
    ESP_LOGI(TAG, "  Key: bl0906_factory_instances");
    ESP_LOGI(TAG, "  Hash: 0x%08X", fnv1_hash("bl0906_factory_instances"));
    ESP_LOGI(TAG, "  存在: %s", list_exists ? "是" : "否");

    if (list_exists) {
        uint32_t count = list_data[0];
        ESP_LOGI(TAG, "  实例数量: %d", count);
        for (uint32_t i = 0; i < count && i < 4; i++) {
            ESP_LOGI(TAG, "    [%d] 0x%08X", i, list_data[i + 1]);
        }
    }

    // 检查备份列表
    auto backup_pref = global_preferences->make_preference<uint32_t[5]>(fnv1_hash("bl0906_instances_backup"));
    uint32_t backup_data[5];
    bool backup_exists = backup_pref.load(&backup_data);

    ESP_LOGI(TAG, "备份列表:");
    ESP_LOGI(TAG, "  Key: bl0906_instances_backup");
    ESP_LOGI(TAG, "  Hash: 0x%08X", fnv1_hash("bl0906_instances_backup"));
    ESP_LOGI(TAG, "  存在: %s", backup_exists ? "是" : "否");

    if (backup_exists) {
        uint32_t backup_count = backup_data[0];
        ESP_LOGI(TAG, "  备份实例数量: %d", backup_count);
        for (uint32_t i = 0; i < backup_count && i < 4; i++) {
            ESP_LOGI(TAG, "    [%d] 0x%08X", i, backup_data[i + 1]);
        }
    }

    ESP_LOGI(TAG, "=== NVS诊断完成 ===");
}

}  // namespace bl0906_factory
}  // namespace esphome
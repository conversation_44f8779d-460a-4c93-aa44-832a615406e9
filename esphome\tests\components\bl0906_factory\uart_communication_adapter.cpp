#ifdef USE_UART_COMMUNICATION_ADAPTER

#include "uart_communication_adapter.h"
#include "bl0906_chip_params.h"
#include "esphome/core/log.h"
#include "esphome/core/helpers.h"
#include <functional>

namespace esphome {
namespace bl0906_factory {

static const char *const TAG = "uart_comm_adapter";

// ========== 设置方法 ==========
// 注意：移除了set_uart_parent方法，因为现在直接在构造函数中设置parent

// ========== CommunicationAdapterInterface 实现 ==========

bool UartCommunicationAdapter::initialize() {
  if (initialized_) {
    return true;
  }
  
  if (!this->parent_) {
    set_error(CommunicationError::HARDWARE_ERROR, "UART父组件未设置");
    return false;
  }
  
  // 重置统计信息
  reset_statistics();
  reset_error_state();
  
  // 清空缓冲区
  flush_buffer();
  
  initialized_ = true;
  ESP_LOGI(TAG, "UART通信适配器初始化成功");
  return true;
}

int32_t UartCommunicationAdapter::read_register(uint8_t address, bool* success) {
  if (!initialized_) {
    set_error(CommunicationError::DEVICE_NOT_AVAILABLE, "适配器未初始化");
    if (success) *success = false;
    return 0;
  }

  // 使用重试机制执行读取操作  
  auto read_operation = [this, address, success]() -> int32_t {
    return this->send_read_command_and_receive(address, success);
  };

  return execute_with_retry<int32_t>(read_operation);
}

bool UartCommunicationAdapter::write_register(uint8_t address, int16_t value) {
  if (!initialized_) {
    set_error(CommunicationError::DEVICE_NOT_AVAILABLE, "适配器未初始化");
    return false;
  }
  
  // 使用重试机制执行写入操作
  auto write_operation = [this, address, value]() -> bool {
    return this->send_write_command(address, value);
  };
  
  return execute_with_retry<bool>(write_operation);
}

bool UartCommunicationAdapter::send_raw_command(const uint8_t* command, size_t length) {
  if (!command || length == 0) {
    set_error(CommunicationError::INVALID_RESPONSE, "无效的原始命令参数");
    update_statistics(false, CommunicationError::INVALID_RESPONSE);
    return false;
  }

  if (!is_available()) {
    set_error(CommunicationError::DEVICE_NOT_AVAILABLE, "UART设备不可用");
    update_statistics(false, CommunicationError::DEVICE_NOT_AVAILABLE);
    return false;
  }

  ESP_LOGV(TAG, "发送原始命令，长度: %zu", length);
  
  // 清空接收缓冲区
  flush_buffer();
  
  // 发送原始命令 - 使用继承的方法
  this->write_array(command, length);
  this->flush();
  
  // 记录发送的命令（用于调试）
  std::string cmd_str = "原始命令: ";
  for (size_t i = 0; i < length; i++) {
    cmd_str += format_hex(command[i]);
    if (i < length - 1) cmd_str += " ";
  }
  ESP_LOGV(TAG, "%s", cmd_str.c_str());
  
  update_statistics(true);
  return true;
}

bool UartCommunicationAdapter::is_available() {
  return initialized_ && this->parent_;
}

bool UartCommunicationAdapter::is_connected() {
  return is_available();
}

void UartCommunicationAdapter::flush_buffer() {
  if (!this->parent_) {
    return;
  }
  
  // 批量读取剩余数据，减少系统调用次数
  constexpr size_t BUFFER_SIZE = 64;
  uint8_t temp_buffer[BUFFER_SIZE];
  
  while (this->available() > 0) {
    size_t bytes_to_read = std::min(static_cast<size_t>(this->available()), BUFFER_SIZE);
    this->read_array(temp_buffer, bytes_to_read);
  }
  
  ESP_LOGV(TAG, "已清空UART接收缓冲区");
}

// 错误处理和统计方法已移至基类

std::string UartCommunicationAdapter::get_adapter_type() const {
  return "UART";
}

std::string UartCommunicationAdapter::get_status_info() const {
  auto stats = get_statistics();
  char buffer[256];
  snprintf(buffer, sizeof(buffer),
           "UART适配器状态: 初始化=%s, 成功=%zu, 错误=%zu, 超时=%zu, 校验和错误=%zu",
           initialized_ ? "是" : "否",
           stats.success_count,
           stats.error_count,
           stats.timeout_count,
           stats.checksum_error_count);
  return std::string(buffer);
}

bool UartCommunicationAdapter::self_test() {
  if (!is_available()) {
    set_error(CommunicationError::DEVICE_NOT_AVAILABLE, "设备不可用");
    return false;
  }
  
  // 尝试读取温度寄存器作为自检
  bool success = false;
  int32_t temp_value = read_register(TEMPERATURE_ADDR, &success);
  
  if (success && temp_value > 0) {
    ESP_LOGI(TAG, "UART适配器自检通过，温度值: %d", temp_value);
    return true;
  } else {
    set_error(CommunicationError::HARDWARE_ERROR, "自检失败：无法读取温度寄存器");
    return false;
  }
}

// ========== 内部方法实现 ==========

bool UartCommunicationAdapter::wait_until_available(size_t len, uint32_t timeout_ms) {
  const uint32_t start = esphome::millis();
  ESP_LOGV(TAG, "等待数据可用，需要%zu字节，当前可用%d字节，超时%ums",
           len, this->available(), timeout_ms);

  while (this->available() < len) {
    if (esphome::millis() - start > timeout_ms) {
      ESP_LOGW(TAG, "等待数据超时，期望%zu字节，实际可用:%d字节", len, this->available());
      return false;
    }

    // 更频繁地让出CPU时间片
    yield();

    // 短暂延时，减轻CPU负担
    esphome::delay(1);
  }

  ESP_LOGV(TAG, "数据可用，需要%zu字节，当前可用%d字节，等待了%ums",
           len, this->available(), (esphome::millis() - start));
  return true;
}

int32_t UartCommunicationAdapter::send_read_command_and_receive(uint8_t address, bool* success) {
  // 清空缓冲区
  flush_buffer();
  
  // 发送读命令 - 使用继承的方法
  this->write_byte(UART_READ_COMMAND);  // UART读命令(0x35)
  this->write_byte(address);                   // 寄存器地址
  this->flush(); // 确保命令已发送

  // 等待响应数据（3字节数据 + 1字节校验和）
  uint8_t response_data[4];
  if (!wait_until_available(sizeof(response_data), UART_TIMEOUT_MS)) {
    set_error(CommunicationError::TIMEOUT, 
              "等待读取寄存器响应数据超时 (地址: 0x" + format_hex(address) + ")");
    if (success) *success = false;
    update_statistics(false, CommunicationError::TIMEOUT);
    return 0;
  }

  // 读取响应数据 - 使用继承的方法
  if (!this->read_array(response_data, sizeof(response_data))) {
    set_error(CommunicationError::INVALID_RESPONSE,
              "读取寄存器数据包失败 (地址: 0x" + format_hex(address) + ")");
    if (success) *success = false;
    update_statistics(false, CommunicationError::INVALID_RESPONSE);
    return 0;
  }

  // 提取数据字节
  uint8_t data_l = response_data[0];    // 低字节
  uint8_t data_m = response_data[1];    // 中字节  
  uint8_t data_h = response_data[2];    // 高字节
  uint8_t received_checksum = response_data[3];  // 校验和

  // 验证校验和
  uint8_t expected_checksum = address + data_l + data_m + data_h;
  expected_checksum = ~expected_checksum;  // 取反
  
  if (received_checksum != expected_checksum) {
    set_error(CommunicationError::CHECKSUM_ERROR,
              "寄存器校验异常 地址:0x" + format_hex(address) + 
              " 计算:" + format_hex(expected_checksum) + 
              " 接收:" + format_hex(received_checksum));
    if (success) *success = false;
    update_statistics(false, CommunicationError::CHECKSUM_ERROR);
    return 0;
  }

// 根据寄存器类型返回正确的数据
  if (is_16bit_register(address)) {
    // 16位寄存器：只使用低16位，处理符号扩展
    int16_t value = (data_m << 8) | data_l;
    ESP_LOGV(TAG, "UART读取16位寄存器 0x%02X: 原始数据[%02X %02X %02X], 16位值: %d", 
             address, data_h, data_m, data_l, value);
    update_statistics(true);
    if (success) *success = true;
    return static_cast<int32_t>(value);
  } else {
    // 24位寄存器
    if (is_unsigned_register(address)) {
      // 无符号24位
      uint32_t value = (static_cast<uint32_t>(data_h) << 16) | 
                       (static_cast<uint32_t>(data_m) << 8) | 
                       static_cast<uint32_t>(data_l);
      ESP_LOGV(TAG, "UART读取无符号24位寄存器 0x%02X: %u", address, value);
      update_statistics(true);
      if (success) *success = true;
      return static_cast<int32_t>(value);
    } else {
      // 带符号24位，需要符号扩展
      int32_t value = (static_cast<int8_t>(data_h) << 16) | 
                      (static_cast<uint32_t>(data_m) << 8) | 
                      static_cast<uint32_t>(data_l);
      ESP_LOGV(TAG, "UART读取有符号24位寄存器 0x%02X: %d", address, value);
      update_statistics(true);
      if (success) *success = true;
      return value;
    }
  }
}

bool UartCommunicationAdapter::send_write_command(uint8_t address, int16_t value) {
  ESP_LOGI(TAG, "正在写入寄存器 0x%02X 值: %d", address, value);

  // 清空接收缓冲区
  flush_buffer();

  // 准备写入数据
  uint8_t l, m, h;

  // 根据寄存器类型决定如何准备数据
  if (is_16bit_register(address)) {
    // 对于16位寄存器（CHGN、CHOS等）
    l = value & 0xFF;          // 低字节
    m = (value >> 8) & 0xFF;   // 中字节(符号位所在的字节)
    h = 0;                     // 高字节为0

    ESP_LOGV(TAG, "16位寄存器写入: 0x%02X <- 0x%02X%02X (值: %d)",
             address, m, l, value);
  } else {
    // 对于24位寄存器（RMSOS等）
    // 使用移位操作来扩展16位有符号数到24位
    int32_t value_24bit = ((int32_t)(value << 8)) >> 8;
    value_24bit &= 0x00FFFFFF;  // 确保只使用低24位

    ESP_LOGV(TAG, "24位寄存器写入: 0x%02X <- 0x%06X (值: %d)",
             address, value_24bit, value);

    // 将24位值拆分为三个字节
    l = value_24bit & 0xFF;          // 低字节
    m = (value_24bit >> 8) & 0xFF;   // 中字节
    h = (value_24bit >> 16) & 0xFF;  // 高字节(符号位所在的字节)
  }

  // 计算校验和
  uint8_t sum = address + l + m + h;
  uint8_t checksum = ~sum;  // 取反

  // 构建发送的字节数组
  uint8_t buffer[6];
  buffer[0] = UART_WRITE_COMMAND;
  buffer[1] = address;
  buffer[2] = l;
  buffer[3] = m;
  buffer[4] = h;
  buffer[5] = checksum;

  // 发送写命令 - 使用继承的方法
  this->write_array(buffer, sizeof(buffer));
  this->flush();

  ESP_LOGV(TAG, "发送写命令: 0x%02X 地址:0x%02X 数据:0x%02X%02X%02X 校验和:0x%02X",
           BL0906_UART_WRITE_COMMAND, address, h, m, l, checksum);

  // 延时等待写入完成
  esphome::delay(5);

  // 读回并验证写入的值
  bool read_success = false;
  int32_t read_value = read_register(address, &read_success);

  if (!read_success) {
    set_error(CommunicationError::INVALID_RESPONSE, "写入验证读取失败");
    update_statistics(false, CommunicationError::INVALID_RESPONSE);
    return false;
  }

  // 比较读回值与期望值
  if (read_value == value) {
    ESP_LOGI(TAG, "寄存器 0x%02X 写入成功，值: %d", address, value);
    update_statistics(true);
    return true;
  } else {
    set_error(CommunicationError::HARDWARE_ERROR,
              "寄存器写入验证失败，写入值=" + std::to_string(value) + 
              "，读回值=" + std::to_string(read_value));
    update_statistics(false, CommunicationError::HARDWARE_ERROR);
    return false;
  }
}

uint8_t UartCommunicationAdapter::calculate_checksum(const uint8_t* data, size_t len) {
  uint8_t sum = 0;
  for (size_t i = 0; i < len; i++) {
    sum += data[i];
  }
  return sum;
}

bool UartCommunicationAdapter::verify_checksum(const std::vector<uint8_t>& data) {
  if (data.size() < 4) {
    return false;
  }
  
  uint8_t calculated = calculate_checksum(data.data(), data.size() - 1);
  calculated = ~calculated;  // 取反
  
  return calculated == data.back();
}

uint32_t UartCommunicationAdapter::to_uint32_t(const ube24_t& data) {
  return ((uint32_t)data.h << 16) | ((uint32_t)data.m << 8) | data.l;
}

int32_t UartCommunicationAdapter::to_int32_t(const sbe24_t& data) {
  uint32_t temp = ((uint32_t)data.h << 16) | ((uint32_t)data.m << 8) | data.l;
  return (temp << 8) >> 8;  // 符号扩展
}

// 寄存器类型判断函数已移至 bl0906_registers.h 作为内联函数
// 直接使用 esphome::bl0906_factory::is_16bit_register() 和 is_unsigned_register()

// 错误处理、统计和重试方法已移至基类

} // namespace bl0906_factory
} // namespace esphome

#endif // USE_UART_COMMUNICATION_ADAPTER 
# BL0906Factory 运行时芯片型号支持 - 代码修改计划

## 📋 修改概述

**目标**：将编译时芯片型号选择改为运行时选择，彻底解决多芯片型号混用问题。

**原则**：
- 🚫 **无需向后兼容** - 完全重构寄存器地址系统
- ✨ **力求简洁** - 移除所有条件编译，统一接口
- 🎯 **性能优先** - 使用缓存和内联函数优化

---

## 🗂️ 文件修改清单

### 1. 核心头文件修改

#### `bl0906_chip_params.h` - 重构寄存器系统
```diff
- 移除所有 #ifdef BL0906_FACTORY_CHIP_MODEL_* 条件编译
- 移除编译时常量数组 I_RMS_ADDR[CHANNEL_COUNT] 等
- 新增运行时芯片参数系统
```

#### `bl0906_factory.h` - 类接口简化
```diff
- 移除 get_register_address() 复杂switch逻辑
- 新增 chip_model_ 成员变量
- 新增缓存优化的地址查找方法
- 简化模板和条件编译相关代码
```

### 2. 实现文件修改

#### `bl0906_factory.cpp` - 核心逻辑重构
```diff
- 重构 convert_raw_to_value() 使用运行时地址查找
- 修改 loop() 状态机使用动态地址
- 优化 read_all_channels_data() 性能
- 移除编译时常量依赖
```

#### `bl0906_number.cpp` - 校准组件适配
```diff
- 修改寄存器地址获取逻辑
- 适配新的地址查找接口
```

### 3. Python配置文件修改

#### `__init__.py` - 移除宏定义生成
```diff
- 移除 cg.add_define(chip_info["macro"]) 
- 简化芯片型号处理逻辑
- 新增运行时芯片型号设置
```

#### `config_mappings.py` - 清理配置映射
```diff
- 移除编译时相关的映射逻辑
- 简化芯片型号配置
```

---

## 🔧 详细修改步骤

### 步骤1: 重构 `bl0906_chip_params.h`

**移除内容：**
```cpp
// 删除所有条件编译
#ifdef BL0906_FACTORY_CHIP_MODEL_BL0910
  #define CHANNEL_COUNT 10
  #define RMSOS_ADDRESSES {0x77, 0x78, ...}
#else
  #define CHANNEL_COUNT 6  
  #define RMSOS_ADDRESSES {0x78, 0x79, ...}
#endif

// 删除编译时数组
static constexpr uint8_t RMSOS_ADDR[CHANNEL_COUNT] = RMSOS_ADDRESSES;
```

**新增内容：**
```cpp
// 芯片型号枚举
enum class ChipModel : uint8_t {
  BL0906 = 0,
  BL0910 = 1
};

// 紧凑的芯片参数结构
struct ChipParams {
  uint8_t channel_count;
  const uint8_t* rmsos_addr;
  const uint8_t* rmsgn_addr;
  const uint8_t* chgn_addr;
  const uint8_t* chos_addr;
  const uint8_t* wattgn_addr;
  const uint8_t* wattos_addr;
  const uint8_t* i_rms_addr;
  const uint8_t* watt_addr;
  const uint8_t* cf_cnt_addr;
};

// 预定义地址数组
static constexpr uint8_t BL0906_RMSOS_ADDRS[] = {0x78, 0x79, 0x7A, 0x7B, 0x7E, 0x7F};
static constexpr uint8_t BL0910_RMSOS_ADDRS[] = {0x77, 0x78, 0x79, 0x7A, 0x7B, 0x7C, 0x7D, 0x7E, 0x7F, 0x80};

// 芯片参数查找表
static constexpr ChipParams CHIP_PARAMS[] = {
  // BL0906
  {6, BL0906_RMSOS_ADDRS, BL0906_RMSGN_ADDRS, ...},
  // BL0910  
  {10, BL0910_RMSOS_ADDRS, BL0910_RMSGN_ADDRS, ...}
};

// 高性能内联查找函数
inline uint8_t get_register_addr(ChipModel chip, CalibRegType type, int channel) {
  const ChipParams& p = CHIP_PARAMS[static_cast<uint8_t>(chip)];
  if (channel >= p.channel_count) return 0;
  
  switch(type) {
    case CalibRegType::RMSOS: return p.rmsos_addr[channel];
    case CalibRegType::RMSGN: return p.rmsgn_addr[channel];
    case CalibRegType::CHGN:  return p.chgn_addr[channel];
    case CalibRegType::CHOS:  return p.chos_addr[channel];
    default: return 0;
  }
}
```

### 步骤2: 简化 `bl0906_factory.h`

**移除内容：**
```cpp
// 删除复杂的 get_register_address() 方法
uint8_t get_register_address(CalibRegType type, int channel) {
  // 通道范围检查
  if (channel < 0 || channel > 6) return 0;
  // 大量switch嵌套逻辑...
}

// 删除编译时常量
static constexpr int get_max_channels() { return MAX_CHANNELS; }
static constexpr const char* get_chip_name() { return CHIP_MODEL_NAME; }
```

**新增内容：**
```cpp
private:
  ChipModel chip_model_ = ChipModel::BL0906;
  const ChipParams* cached_params_ = &CHIP_PARAMS[0];

public:
  // 设置芯片型号
  void set_chip_model(ChipModel model) {
    chip_model_ = model;
    cached_params_ = &CHIP_PARAMS[static_cast<uint8_t>(model)];
  }
  
  // 直接访问芯片参数，无需包装函数
  const ChipParams& get_chip_params() const { return *cached_params_; }
  ChipModel get_chip_model() const { return chip_model_; }
```

### 步骤3: 重构 `bl0906_factory.cpp`

**关键修改点：**

1. **convert_raw_to_value() 方法**
```cpp
// 替换编译时数组访问，直接使用get_register_addr()
// 旧代码：
for (int i = 0; i < MAX_CHANNELS; i++) {
  if (address == I_RMS_ADDR[i]) {
    return static_cast<uint32_t>(raw_value) / Ki;
  }
}

// 新代码（直接调用，无包装函数）：
for (int i = 0; i < cached_params_->channel_count; i++) {
  if (address == get_register_addr(chip_model_, CalibRegType::I_RMS, i)) {
    return static_cast<uint32_t>(raw_value) / Ki;
  }
}
```

2. **loop() 状态机**
```cpp
// 替换编译时常量，直接使用常量值
// 旧代码：
int32_t temp_raw = send_read_command_and_receive(TEMPERATURE_ADDR, &success);

// 新代码：
int32_t temp_raw = send_read_command_and_receive(0x5E, &success); // 直接使用地址常量
```

3. **read_all_channels_data() 方法**
```cpp
// 动态通道数循环，直接调用get_register_addr()
for (int i = 0; i < cached_params_->channel_count; i++) {
  current_data_.channels[i].current_raw = send_read_command_and_receive(
    get_register_addr(chip_model_, CalibRegType::I_RMS, i), &success);
  current_data_.channels[i].watt_raw = send_read_command_and_receive(
    get_register_addr(chip_model_, CalibRegType::WATT, i), &success);
  // 直接调用，避免多层函数包装
}
```

### 步骤4: 修改 `__init__.py`

**移除内容：**
```python
# 删除宏定义生成
chip_info = CHIP_MODELS[chip_model]
cg.add_define(chip_info["macro"])

# 删除调试信息
cg.add(cg.RawStatement(f'ESP_LOGI("bl0906_factory", "Compiled for {chip_model.upper()}");'))
```

**新增内容：**
```python
# 设置芯片型号
chip_model = config.get(CONF_CHIP_MODEL, "bl0906")
chip_enum_map = {
    "bl0906": "esphome::bl0906_factory::ChipModel::BL0906",
    "bl0910": "esphome::bl0906_factory::ChipModel::BL0910"
}
cg.add(var.set_chip_model(cg.RawExpression(chip_enum_map[chip_model])))
```

### 步骤5: 清理 `config_mappings.py`

**简化内容：**
```python
# 简化芯片型号配置
CHIP_MODELS = {
    "bl0906": {"max_channels": 6},
    "bl0910": {"max_channels": 10}
}
# 移除 "macro" 字段
```

---

## 🧪 测试计划

### 1. 单元测试
- 测试 `get_register_addr()` 函数正确性
- 验证两种芯片型号的地址映射
- 性能基准测试

### 2. 集成测试  
- 单个BL0906实例测试
- 单个BL0910实例测试
- 混合实例测试（BL0906 + BL0910）

### 3. 性能测试
- 对比修改前后的性能差异
- 验证缓存优化效果

---

## ⚡ 性能优化要点

1. **使用缓存指针** - `cached_params_` 避免重复查找
2. **内联函数** - `inline` 关键字减少函数调用开销
3. **紧凑数据结构** - `ChipParams` 结构体优化内存布局
4. **编译器优化** - 利用 `constexpr` 和 `static` 优化

---

## 🎯 预期效果

1. **解决问题**：彻底解决BL0906使用BL0910地址的问题
2. **简化代码**：移除所有条件编译，代码更清晰
3. **性能保证**：运行时开销 < 0.001%，用户无感知
4. **扩展性**：未来新增芯片型号只需添加参数配置

---

**总结：这个修改计划将彻底解决当前的架构问题，实现简洁高效的运行时芯片型号支持。** 
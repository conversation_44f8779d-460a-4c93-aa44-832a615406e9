packages: 
  bl0906: !include packages/16ch_home_bl0906_calib.yaml
substitutions:
  name: "16-ch-monitor-home"
  friendly_name: "16-ch-energy-monitor-home"
  ch1: "Light"   #可根据需要修改名称
  ch2: "Kitchen Roaster"   #可根据需要修改名称
  ch3: "Bedroom1 Plug"    #可根据需要修改名称
  ch4: "Washing2 Plug"    #可根据需要修改名称
  ch5: "Dining Plug"  #可根据需要修改名称
  ch6: "ch_6"     #可根据需要修改名称
  ch7: "ch_7"     #可根据需要修改名称
  ch8: "ch_8"     #可根据需要修改名称
  ch9: "ch_9"     #可根据需要修改名称
  ch10: "ch_10"     #可根据需要修改名称
  ch11: "AC exaust"   #可根据需要修改名称
  ch12: "Kitchen Vaccum"   #可根据需要修改名称
  ch13: "Cinema"    #可根据需要修改名称
  ch14: "Cabinets"    #可根据需要修改名称
  ch15: "AC compressor"  #可根据需要修改名称
  ch16: "ch_16"     #可根据需要修改名称
esphome:
  name: "${name}"
  friendly_name: "${friendly_name}"
preferences:
  flash_write_interval: 10min
esp32:
  board: esp32dev
  framework:
    type: arduino

# Enable logging
logger:
  level: DEBUG
  logs:
    sensor: WARN
  #   mqtt.component: WARN
  #   mqtt.client: ERROR
  baud_rate: 0

# Enable Home Assistant API
api:
  encryption:
    key: "c8OIaeVFYiA5olgZlIJnxVjWeIISZb2l5SKs6nUI1fE="

ota:
  - platform: esphome
  
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

  # Enable fallback hotspot (captive portal) in case wifi connection fails
  ap:
    ssid: "16-Ch-Monitor-Home"
    password: ""

captive_portal:

web_server:
  port: 80
  local: True
  version: 3
  sorting_groups:
    - id: calibrate
      name: "Calibrate"
      sorting_weight: 0
    - id: bl0910_sensors
      name: "BL0910 Basic Sensors"
      sorting_weight: 5
    - id: bl0910_current
      name: "BL0910 Current (CH1-10)"
      sorting_weight: 10
    - id: bl0910_power
      name: "BL0910 Power (CH1-10)"
      sorting_weight: 15
    - id: bl0910_energy
      name: "BL0910 Energy (CH1-10)"
      sorting_weight: 20
    - id: bl0910_total_energy
      name: "BL0910 Total Energy (CH1-10)"
      sorting_weight: 25
    - id: bl0910_energy_last
      name: "BL0910 Energy Last (CH1-10)"
      sorting_weight: 30
    - id: pzem_004T
      name: "PZEM-004T Sensors"
      sorting_weight: 35
    - id: current
      name: "BL0906 Current (CH11-16)"
      sorting_weight: 40
    - id: power
      name: "BL0906 Power (CH11-16)"
      sorting_weight: 45
    - id: energy
      name: "BL0906 Energy (CH11-16)"
      sorting_weight: 50
    - id: energy_stats
      name: "BL0906 Energy Statistics"
      sorting_weight: 55
    - id: bl0906_calibration
      name: "BL0906 Calibration Settings"
      sorting_weight: 60
    - id: bl0906_controls
      name: "BL0906 Control Buttons"
      sorting_weight: 65
    - id: system_monitoring
      name: "System Monitoring"
      sorting_weight: 70
    - id: network_info
      name: "Network Information"
      sorting_weight: 75
    - id: miscellaneous
      name: "Miscellaneous"
      sorting_weight: 80
# external_components:
#   - source:
#       type: git
#       url: https://github.com/carrot8848/ESPHome
#       ref: main
external_components:
  - source:
      type: local
      path: "components"
    refresh: 0s
status_led:
  pin: 
    number: GPIO13
    inverted: true
uart:
  - id: uart_bus1
    rx_pin: 33
    tx_pin: 32
    baud_rate: 9600
  - id: uart_bus2
    rx_pin: 16
    tx_pin: 17
    baud_rate: 19200
  - id: uart_bus3
    rx_pin: 14
    tx_pin: 27
    baud_rate: 9600
modbus:
  - uart_id: uart_bus3
    id: mod_bus1

sensor:
  - platform: pzemac
    id: sensor_pzem
    update_interval: 10s
    modbus_id: mod_bus1
    current:
      name: "PZEM Current"
      web_server:
        sorting_group_id: pzem_004T
    voltage:
      name: "PZEM Voltage"
      web_server:
        sorting_group_id: pzem_004T
    energy:
      name: "PZEM Energy"
      id: pzem_energy
      filters:
        # Multiplication factor from Wh to kWh is 0.001
        - multiply: 0.001
      unit_of_measurement: kWh
      web_server:
        sorting_group_id: pzem_004T
    power:
      name: "PZEM Power"
      id: pzemac_power
      filters:
        # Multiplication factor from W to kW is 0.001
       - multiply: 0.001
      unit_of_measurement: kW
      web_server:
        sorting_group_id: pzem_004T
    frequency:
      name: "PZEM Frequency"
      web_server:
        sorting_group_id: pzem_004T
    power_factor:
      name: "PZEM Power Factor"
      web_server:
        sorting_group_id: pzem_004T
    
  - platform: wifi_signal # Reports the WiFi signal strength/RSSI in dB
    name: "Energy_meter WiFi Signal dB"
    id: wifi_signal_db
    update_interval: 60s
    entity_category: "diagnostic"
    web_server:
      sorting_group_id: network_info

  - platform: copy # Reports the WiFi signal strength in %
    source_id: wifi_signal_db
    name: "Energy_meter WiFi Signal Percent"
    filters:
      - lambda: return min(max(2 * (x + 100.0), 0.0), 100.0);
    unit_of_measurement: "%"
    entity_category: "diagnostic"
    web_server:
      sorting_group_id: network_info

  - platform: debug
    free:
      name: "Heap Free"
      web_server:
        sorting_group_id: system_monitoring
    block:
      name: "Heap Max Block"
      web_server:
        sorting_group_id: system_monitoring
    loop_time:
      name: "Loop Time"
      web_server:
        sorting_group_id: system_monitoring
debug:
  update_interval: 5s
switch:
  - platform: restart
    name: "${name} controller Restart"
    web_server:
      sorting_group_id: miscellaneous
  - platform: factory_reset
    name: Restart with Factory Default Settings
    web_server:
      sorting_group_id: miscellaneous

time:
  - platform: homeassistant
    id: my_time

text_sensor:
  - platform: wifi_info
    ip_address:
      name: Energy_meter IP Address
      web_server:
        sorting_group_id: network_info
    ssid:
      name: Energy_meter Connected SSID
      web_server:
        sorting_group_id: network_info
    bssid:
      name: Energy_meter Connected BSSID
      web_server:
        sorting_group_id: network_info
    mac_address:
      name: Energy_meter Mac Wifi Address
      web_server:
        sorting_group_id: network_info
    # scan_results:
    #   name: Energy_meter Latest Scan Results
    dns_address:
      name: Energy_meter DNS Address
      web_server:
        sorting_group_id: network_info
  - platform: debug
    device:
      name: "Device Info"
      web_server:
        sorting_group_id: system_monitoring
    reset_reason:
      name: "Reset Reason"
      web_server:
        sorting_group_id: system_monitoring 